from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.orm import relationship
from app.models.base import BaseModel
from app.db.session import Base


class User(Base, BaseModel):
    """User model for authentication and user management"""

    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)

    # Relationships
    clients = relationship("Client", back_populates="user", cascade="all, delete-orphan")
    invoices = relationship("Invoice", back_populates="user", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="user", cascade="all, delete-orphan")
