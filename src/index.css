@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary: 30 58 138; /* blue-900 */
    --color-accent: 16 185 129; /* emerald-500 */
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Dark mode styles */
  .dark body {
    @apply bg-gray-900 text-gray-100;
  }

  .dark .bg-white {
    @apply bg-gray-800;
  }

  .dark .text-gray-900 {
    @apply text-gray-100;
  }

  .dark .text-gray-700 {
    @apply text-gray-300;
  }

  .dark .text-gray-600 {
    @apply text-gray-400;
  }

  .dark .text-gray-500 {
    @apply text-gray-400;
  }

  .dark .border-gray-100,
  .dark .border-gray-200 {
    @apply border-gray-700;
  }

  .dark .bg-gray-50 {
    @apply bg-gray-800;
  }

  .dark .bg-gray-100 {
    @apply bg-gray-700;
  }

  .dark .hover\:bg-gray-50:hover {
    @apply hover:bg-gray-700;
  }

  .dark .hover\:bg-gray-100:hover {
    @apply hover:bg-gray-700;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-slide-in {
  animation: slideInRight 0.3s ease-out;
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out;
}

/* Custom scroll bar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #cbcbcb;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #777;
}