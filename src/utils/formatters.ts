/**
 * Utility functions for formatting data
 */

// Cache for exchange rates
interface ExchangeRateCache {
  rates: Record<string, Record<string, number>>;
  lastUpdated: number;
}

let exchangeRateCache: ExchangeRateCache | null = null;
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Fetch current exchange rates from API
 */
async function fetchExchangeRates(): Promise<Record<string, Record<string, number>>> {
  try {
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
    const data = await response.json();

    if (data.rates) {
      // Create reverse rates for XAF to other currencies
      const usdRates = data.rates;
      const xafToUsd = 1 / usdRates.XAF;
      const xafRates: Record<string, number> = {};

      Object.keys(usdRates).forEach(currency => {
        if (currency !== 'XAF') {
          xafRates[currency] = xafToUsd * usdRates[currency];
        }
      });
      xafRates.USD = xafToUsd;

      return {
        USD: usdRates,
        XAF: xafRates
      };
    }
  } catch (error) {
    console.warn('Failed to fetch exchange rates from API:', error);
  }

  // Fallback rates if API fails
  return {
    'USD': {
      'XAF': 560.25,
      'EUR': 0.854,
      'GBP': 0.73,
      'CAD': 1.37,
      'AUD': 1.53,
      'JPY': 144.65,
      'CNY': 7.17,
      'INR': 85.56,
      'NGN': 1540.38,
      'ZAR': 17.82,
      'KES': 129.14,
      'GHS': 10.79
    },
    'XAF': {
      'USD': 0.00178,
      'EUR': 0.00152,
      'GBP': 0.0013,
      'CAD': 0.00244,
      'AUD': 0.00273,
      'JPY': 0.258,
      'CNY': 0.0128,
      'INR': 0.153,
      'NGN': 2.75,
      'ZAR': 0.0318,
      'KES': 0.230,
      'GHS': 0.0192
    }
  };
}

/**
 * Get cached exchange rates or fetch new ones
 */
async function getCachedExchangeRates(): Promise<Record<string, Record<string, number>>> {
  const now = Date.now();

  // Check if cache is valid
  if (exchangeRateCache && (now - exchangeRateCache.lastUpdated) < CACHE_DURATION) {
    return exchangeRateCache.rates;
  }

  // Fetch new rates
  const rates = await fetchExchangeRates();

  // Update cache
  exchangeRateCache = {
    rates,
    lastUpdated: now
  };

  return rates;
}

/**
 * Format a date string to a more readable format
 * @param dateString - Date string in ISO format (YYYY-MM-DD)
 * @returns Formatted date string (e.g., "Jan 1, 2023")
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return dateString;
  }

  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(date);
};

// Currency configuration
export interface CurrencyConfig {
  code: string;
  name: string;
  symbol: string;
  locale?: string;
  decimalPlaces?: number;
}

// Common currencies with their symbols and codes
export const currencies: CurrencyConfig[] = [
  { code: 'XAF', name: 'Central African CFA franc', symbol: 'FCFA', locale: 'fr-FR', decimalPlaces: 0 },
  { code: 'USD', name: 'US Dollar', symbol: '$', locale: 'en-US' },
  { code: 'EUR', name: 'Euro', symbol: '€', locale: 'de-DE' },
  { code: 'GBP', name: 'British Pound', symbol: '£', locale: 'en-GB' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', locale: 'en-CA' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', locale: 'en-AU' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', locale: 'ja-JP', decimalPlaces: 0 },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', locale: 'zh-CN' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', locale: 'en-IN' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦', locale: 'en-NG' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', locale: 'en-ZA' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', locale: 'en-KE' },
  { code: 'GHS', name: 'Ghanaian Cedi', symbol: '₵', locale: 'en-GH' },
];

// Get currency configuration by code
export const getCurrencyConfig = (code: string): CurrencyConfig => {
  return currencies.find(c => c.code === code) || currencies[0];
};

/**
 * Format a number as currency
 * @param amount - Number to format as currency
 * @param currencyCode - Currency code (default: XAF)
 * @param options - Additional formatting options
 * @returns Formatted currency string (e.g., "FCFA 1,235")
 */
export const formatCurrency = (
  amount: number,
  currencyCode = 'XAF',
  options?: {
    showSymbol?: boolean;
    showCode?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
): string => {
  if (amount === undefined || amount === null) return '';

  const currencyConfig = getCurrencyConfig(currencyCode);
  const locale = currencyConfig.locale || 'en-US';
  const decimalPlaces = currencyConfig.decimalPlaces !== undefined ? currencyConfig.decimalPlaces : 2;

  const minimumFractionDigits = options?.minimumFractionDigits !== undefined
    ? options.minimumFractionDigits
    : decimalPlaces;

  const maximumFractionDigits = options?.maximumFractionDigits !== undefined
    ? options.maximumFractionDigits
    : decimalPlaces;

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits,
    maximumFractionDigits,
    currencyDisplay: options?.showSymbol === false ? 'code' : 'symbol'
  }).format(amount);
};

/**
 * Convert amount from one currency to another using live exchange rates
 * @param amount - Amount to convert
 * @param fromCurrency - Source currency code
 * @param toCurrency - Target currency code
 * @returns Promise that resolves to converted amount
 */
export const convertCurrency = async (
  amount: number,
  fromCurrency: string,
  toCurrency: string
): Promise<number> => {
  if (fromCurrency === toCurrency) return amount;

  // Get exchange rates from cache or API
  const exchangeRates = await getCachedExchangeRates();

  // Convert to USD first if direct rate not available
  if (fromCurrency !== 'USD' && !exchangeRates[fromCurrency]) {
    const usdAmount = amount / exchangeRates['USD'][fromCurrency];
    return await convertCurrency(usdAmount, 'USD', toCurrency);
  }

  // Direct conversion
  if (exchangeRates[fromCurrency] && exchangeRates[fromCurrency][toCurrency]) {
    return amount * exchangeRates[fromCurrency][toCurrency];
  }

  // Convert through USD
  if (exchangeRates['USD'][fromCurrency] && exchangeRates['USD'][toCurrency]) {
    const usdAmount = amount / exchangeRates['USD'][fromCurrency];
    return usdAmount * exchangeRates['USD'][toCurrency];
  }

  // Fallback: no conversion
  console.warn(`No exchange rate found for ${fromCurrency} to ${toCurrency}`);
  return amount;
};

/**
 * Convert USD amount to XAF and format as currency
 * @param usdAmount - Amount in USD
 * @param options - Formatting options
 * @returns Formatted XAF amount
 */
export const formatUSDtoXAF = async (
  usdAmount: number,
  options?: {
    showSymbol?: boolean;
    showCode?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
): Promise<string> => {
  if (usdAmount === undefined || usdAmount === null) return '';

  try {
    const xafAmount = await convertCurrency(usdAmount, 'USD', 'XAF');
    return formatCurrency(xafAmount, 'XAF', options);
  } catch (error) {
    console.error('Error converting USD to XAF:', error);
    // Fallback to USD formatting if conversion fails
    return formatCurrency(usdAmount, 'USD', options);
  }
};

/**
 * Synchronous version using cached rates for immediate display
 * @param usdAmount - Amount in USD
 * @param options - Formatting options
 * @returns Formatted XAF amount using cached rates
 */
export const formatUSDtoXAFSync = (
  usdAmount: number,
  options?: {
    showSymbol?: boolean;
    showCode?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
): string => {
  if (usdAmount === undefined || usdAmount === null) return '';

  // Use cached rate or fallback
  const fallbackRate = 560.25; // Current USD to XAF rate
  let xafAmount = usdAmount * fallbackRate;

  // Try to use cached rate if available
  if (exchangeRateCache && exchangeRateCache.rates.USD && exchangeRateCache.rates.USD.XAF) {
    xafAmount = usdAmount * exchangeRateCache.rates.USD.XAF;
  }

  return formatCurrency(xafAmount, 'XAF', options);
};

/**
 * Format a number as a percentage
 * @param value - Number to format as percentage
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted percentage string (e.g., "12.34%")
 */
export const formatPercentage = (value: number, decimals = 2): string => {
  if (value === undefined || value === null) return '';

  return `${value.toFixed(decimals)}%`;
};

/**
 * Format a phone number to a standard format
 * @param phone - Phone number string
 * @returns Formatted phone number (e.g., "(*************")
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';

  // Remove all non-numeric characters
  const cleaned = phone.replace(/\D/g, '');

  // Check if it's a valid US phone number
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);

  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }

  // Return original if not a standard format
  return phone;
};

/**
 * Truncate text to a specified length and add ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length before truncation (default: 50)
 * @returns Truncated text with ellipsis if needed
 */
export const truncateText = (text: string, maxLength = 50): string => {
  if (!text) return '';

  if (text.length <= maxLength) {
    return text;
  }

  return `${text.substring(0, maxLength)}...`;
};

/**
 * Format a file size in bytes to a human-readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size (e.g., "1.23 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * Format a time string to a localized time format
 * @param timeString - ISO date string or Date object
 * @returns Formatted time string (e.g., "3:45 PM")
 */
export const formatTime = (
  timeString: string | Date
): string => {
  if (!timeString) return 'N/A';

  try {
    const date = typeof timeString === 'string' ? new Date(timeString) : timeString;
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Invalid time';
  }
};

/**
 * Format a date range
 * @param startDate - Start date string or Date object
 * @param endDate - End date string or Date object
 * @returns Formatted date range string
 */
export const formatDateRange = (
  startDate: string | Date,
  endDate: string | Date
): string => {
  if (!startDate || !endDate) return 'N/A';

  try {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

    // If same day, show only one date
    if (
      start.getFullYear() === end.getFullYear() &&
      start.getMonth() === end.getMonth() &&
      start.getDate() === end.getDate()
    ) {
      return `${formatDate(start.toISOString())}`;
    }

    // If same month and year, show abbreviated format
    if (
      start.getFullYear() === end.getFullYear() &&
      start.getMonth() === end.getMonth()
    ) {
      return `${start.getDate()} - ${formatDate(end.toISOString())}`;
    }

    // Otherwise show full range
    return `${formatDate(start.toISOString())} - ${formatDate(end.toISOString())}`;
  } catch (error) {
    console.error('Error formatting date range:', error);
    return 'Invalid date range';
  }
};

/**
 * Format a relative time (time ago)
 * @param dateString - ISO date string or Date object
 * @returns Formatted relative time string (e.g., "2 hours ago")
 */
export const formatTimeAgo = (dateString: string | Date): string => {
  if (!dateString) return 'N/A';

  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);
    const diffWeek = Math.round(diffDay / 7);
    const diffMonth = Math.round(diffDay / 30);
    const diffYear = Math.round(diffDay / 365);

    if (diffSec < 60) return `${diffSec} seconds ago`;
    if (diffMin < 60) return `${diffMin} minutes ago`;
    if (diffHour < 24) return `${diffHour} hours ago`;
    if (diffDay < 7) return `${diffDay} days ago`;
    if (diffWeek < 4) return `${diffWeek} weeks ago`;
    if (diffMonth < 12) return `${diffMonth} months ago`;
    return `${diffYear} years ago`;
  } catch (error) {
    console.error('Error formatting time ago:', error);
    return 'Invalid date';
  }
};
