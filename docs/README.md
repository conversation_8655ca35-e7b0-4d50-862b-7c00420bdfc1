# I-Invoyisi Documentation

Welcome to the I-Invoyisi documentation. This directory contains comprehensive documentation for the I-Invoyisi invoice management system.

## Contents

- [Getting Started](./getting-started.md) - Installation and setup instructions
- [User Manual](./user-manual/README.md) - Detailed guide on how to use the application
- [System Design](./system-design/README.md) - Architecture and technical design documentation
- [Class Diagrams](./diagrams/README.md) - Class and sequence diagrams

## About I-Invoyisi

I-Invoyisi is a smart invoice management system powered by artificial intelligence. It helps businesses streamline their invoicing process, from creation to payment tracking, with features like:

- AI-powered document processing
- Client management
- Invoice creation and tracking
- Payment recording
- Financial reporting
- Dashboard analytics

## Documentation Structure

```
docs/
├── README.md                 # This file
├── getting-started.md        # Installation and setup guide
├── images/                   # Documentation images
├── user-manual/              # User manual and guides
│   └── README.md             # Main user manual
├── system-design/            # System architecture documentation
│   └── README.md             # System design document
└── diagrams/                 # UML and sequence diagrams
    └── README.md             # Diagrams documentation
```

## Contributing to Documentation

If you'd like to contribute to this documentation, please follow these guidelines:

1. Use Markdown for all documentation files
2. Place images in the appropriate subdirectory of the `images` folder
3. Follow the existing structure and style
4. Update the main README.md file when adding new documents

## Contact

For questions or support regarding this documentation, please contact:

- Developer: Mappa Tetong Caline Staelle
- Email: [<EMAIL>](mailto:<EMAIL>)
- GitHub: [https://github.com/Tcaline10/Tcaline.git](https://github.com/Tcaline10/Tcaline.git)
